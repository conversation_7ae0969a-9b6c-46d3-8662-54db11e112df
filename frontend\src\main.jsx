import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { HelmetProvider } from 'react-helmet-async';
import { ErrorBoundary } from 'react-error-boundary';

// Bootstrap CSS
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap-icons/font/bootstrap-icons.css';

// Custom styles
import './styles/index.css';
import './styles/variables.css';

// Main App component
import App from './App.jsx';

// Error Fallback Component
import ErrorFallback from './components/common/ErrorFallback.jsx';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Error handler for ErrorBoundary
const errorHandler = (error, errorInfo) => {
  console.error('Application Error:', error, errorInfo);
  // Here you can send error to logging service
};

console.log('main.jsx: Starting React app');

const rootElement = document.getElementById('root');
console.log('main.jsx: Root element found:', !!rootElement);

if (!rootElement) {
  console.error('main.jsx: Root element not found!');
} else {
  console.log('main.jsx: Creating React root and rendering app');

  ReactDOM.createRoot(rootElement).render(
    <React.StrictMode>
      <ErrorBoundary FallbackComponent={ErrorFallback} onError={errorHandler}>
        <HelmetProvider>
          <QueryClientProvider client={queryClient}>
            <BrowserRouter>
              <App />
            </BrowserRouter>
            {import.meta.env.VITE_ENABLE_REACT_QUERY_DEVTOOLS === 'true' && (
              <ReactQueryDevtools initialIsOpen={false} />
            )}
          </QueryClientProvider>
        </HelmetProvider>
      </ErrorBoundary>
    </React.StrictMode>
  );

  console.log('main.jsx: React app rendered');
}
