import { useState, useEffect } from 'react';

// Mock authentication hook - will be replaced with real implementation later
export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);

  useEffect(() => {
    // Simulate checking authentication status
    const checkAuth = async () => {
      try {
        // Check if user is logged in (check localStorage, validate token, etc.)
        const token = localStorage.getItem(import.meta.env.VITE_JWT_STORAGE_KEY || 'tallycrm_token');
        const userData = localStorage.getItem(import.meta.env.VITE_USER_STORAGE_KEY || 'tallycrm_user');

        if (token && userData) {
          // TODO: Validate token with backend
          setUser(JSON.parse(userData));
          setIsAuthenticated(true);
        } else {
          // For development: Auto-login with demo user
          const demoUser = {
            id: 1,
            name: 'Demo User',
            email: '<EMAIL>',
            role: 'admin',
            permissions: ['read', 'write', 'delete']
          };

          const demoToken = 'demo-jwt-token';

          // Store in localStorage
          localStorage.setItem(import.meta.env.VITE_JWT_STORAGE_KEY || 'tallycrm_token', demoToken);
          localStorage.setItem(import.meta.env.VITE_USER_STORAGE_KEY || 'tallycrm_user', JSON.stringify(demoUser));

          setUser(demoUser);
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (credentials) => {
    try {
      setIsLoading(true);

      // TODO: Replace with actual API call
      console.log('Login with:', credentials);

      // Mock successful login
      const mockUser = {
        id: 1,
        name: 'John Doe',
        email: credentials.email,
        role: 'admin',
        permissions: ['read', 'write', 'delete']
      };

      const mockToken = 'mock-jwt-token';

      // Store in localStorage
      localStorage.setItem(import.meta.env.VITE_JWT_STORAGE_KEY || 'tallycrm_token', mockToken);
      localStorage.setItem(import.meta.env.VITE_USER_STORAGE_KEY || 'tallycrm_user', JSON.stringify(mockUser));

      setUser(mockUser);
      setIsAuthenticated(true);

      return { success: true, user: mockUser };
    } catch (error) {
      console.error('Login failed:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    // Clear localStorage
    localStorage.removeItem(import.meta.env.VITE_JWT_STORAGE_KEY || 'tallycrm_token');
    localStorage.removeItem(import.meta.env.VITE_USER_STORAGE_KEY || 'tallycrm_user');
    localStorage.removeItem(import.meta.env.VITE_REFRESH_TOKEN_KEY || 'tallycrm_refresh_token');

    setUser(null);
    setIsAuthenticated(false);
  };

  const updateUser = (userData) => {
    setUser(userData);
    localStorage.setItem(import.meta.env.VITE_USER_STORAGE_KEY || 'tallycrm_user', JSON.stringify(userData));
  };

  return {
    isAuthenticated,
    isLoading,
    user,
    login,
    logout,
    updateUser
  };
};
