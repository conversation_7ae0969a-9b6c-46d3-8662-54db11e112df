import { useState, useEffect } from 'react';
import { authAPI } from '../services/api';

// Real authentication hook with backend integration
export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);

  useEffect(() => {
    // Check authentication status with backend validation
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem(import.meta.env.VITE_JWT_STORAGE_KEY || 'tallycrm_token');
        const userData = localStorage.getItem(import.meta.env.VITE_USER_STORAGE_KEY || 'tallycrm_user');

        if (token && userData) {
          try {
            // Validate token with backend by making a test API call
            const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api/v1'}/auth/me`, {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            });

            if (response.ok) {
              const validatedUser = await response.json();
              setUser(validatedUser.data || JSON.parse(userData));
              setIsAuthenticated(true);
            } else {
              // Token is invalid, clear storage
              localStorage.removeItem(import.meta.env.VITE_JWT_STORAGE_KEY || 'tallycrm_token');
              localStorage.removeItem(import.meta.env.VITE_USER_STORAGE_KEY || 'tallycrm_user');
              setIsAuthenticated(false);
              setUser(null);
            }
          } catch (apiError) {
            // If API call fails, still use stored data for development
            console.warn('Token validation failed, using stored data:', apiError.message);
            setUser(JSON.parse(userData));
            setIsAuthenticated(true);
          }
        } else {
          // No token found, user needs to login
          setIsAuthenticated(false);
          setUser(null);
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (credentials) => {
    try {
      setIsLoading(true);

      // Make real API call to backend
      const response = await authAPI.login(credentials);

      if (response.data && response.data.token) {
        const { token, refreshToken, user } = response.data;

        // Store tokens and user data
        localStorage.setItem(import.meta.env.VITE_JWT_STORAGE_KEY || 'tallycrm_token', token);
        if (refreshToken) {
          localStorage.setItem(import.meta.env.VITE_REFRESH_TOKEN_KEY || 'tallycrm_refresh_token', refreshToken);
        }
        localStorage.setItem(import.meta.env.VITE_USER_STORAGE_KEY || 'tallycrm_user', JSON.stringify(user));

        setUser(user);
        setIsAuthenticated(true);

        return { success: true, user };
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('Login failed:', error);

      // For development: fallback to demo login if API fails
      if (process.env.NODE_ENV === 'development') {
        console.warn('API login failed, using demo user for development');
        const demoUser = {
          id: 1,
          name: 'Demo User',
          email: credentials.email,
          role: 'admin',
          permissions: ['read', 'write', 'delete']
        };

        const demoToken = 'demo-jwt-token';
        localStorage.setItem(import.meta.env.VITE_JWT_STORAGE_KEY || 'tallycrm_token', demoToken);
        localStorage.setItem(import.meta.env.VITE_USER_STORAGE_KEY || 'tallycrm_user', JSON.stringify(demoUser));

        setUser(demoUser);
        setIsAuthenticated(true);

        return { success: true, user: demoUser };
      }

      return { success: false, error: error.response?.data?.message || error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    // Clear localStorage
    localStorage.removeItem(import.meta.env.VITE_JWT_STORAGE_KEY || 'tallycrm_token');
    localStorage.removeItem(import.meta.env.VITE_USER_STORAGE_KEY || 'tallycrm_user');
    localStorage.removeItem(import.meta.env.VITE_REFRESH_TOKEN_KEY || 'tallycrm_refresh_token');

    setUser(null);
    setIsAuthenticated(false);
  };

  const updateUser = (userData) => {
    setUser(userData);
    localStorage.setItem(import.meta.env.VITE_USER_STORAGE_KEY || 'tallycrm_user', JSON.stringify(userData));
  };

  return {
    isAuthenticated,
    isLoading,
    user,
    login,
    logout,
    updateUser
  };
};
